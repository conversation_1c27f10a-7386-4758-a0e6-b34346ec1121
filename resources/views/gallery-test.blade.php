<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Gallery Functionality Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Authentication & Settings Status</h2>
            <div class="space-y-2">
                <p><strong>User Authenticated:</strong> {{ auth()->check() ? 'YES' : 'NO' }}</p>
                @if(auth()->check())
                    <p><strong>User ID:</strong> {{ auth()->id() }}</p>
                    <p><strong>User Name:</strong> {{ auth()->user()->name }}</p>
                    <p><strong>Gallery Feature Enabled:</strong> {{ \App\Models\Feature::isEnabled('gallery') ? 'YES' : 'NO' }}</p>
                    <p><strong>User Gallery Images Enabled:</strong> {{ auth()->user()->show_gallery_images ? 'YES' : 'NO' }}</p>
                    <p><strong>Gallery Images Count:</strong> {{ auth()->user()->galleryImages->count() }}</p>
                    <p><strong>Gallery Tab Should Show:</strong> {{ (\App\Models\Feature::isEnabled('gallery') && auth()->user()->show_gallery_images) ? 'YES' : 'NO' }}</p>
                @endif
            </div>
        </div>

        @if(auth()->check() && \App\Models\Feature::isEnabled('gallery') && auth()->user()->show_gallery_images)
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Gallery Images</h2>
                @if(auth()->user()->galleryImages->count() > 0)
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4" id="test-gallery-grid">
                        @foreach(auth()->user()->galleryImages as $index => $image)
                            <div class="gallery-item relative group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200" data-id="{{ $image->id }}" data-index="{{ $index }}">
                                <div class="aspect-square">
                                    <img src="{{ $image->image_url }}" alt="Gallery Image" loading="lazy" class="w-full h-full object-cover cursor-pointer" onclick="testOpenLightbox('{{ $image->image_url }}', {{ $index }})">
                                </div>
                                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"></div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">No gallery images found.</p>
                @endif
            </div>

            <!-- Test Lightbox -->
            <div id="test-lightbox" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
                <div class="relative max-w-4xl max-h-full p-4">
                    <img id="test-lightbox-image" src="" alt="Gallery Image" class="max-w-full max-h-full object-contain rounded-lg">
                    
                    <!-- Close Button -->
                    <button onclick="closeTestLightbox()" class="absolute top-2 right-2 w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 text-white rounded-full flex items-center justify-center transition-all duration-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    
                    <!-- Image Counter -->
                    <div id="test-lightbox-counter" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                        <span id="test-current-image-number">1</span> / <span id="test-total-images">1</span>
                    </div>
                </div>
            </div>
        @else
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                <p><strong>Gallery not available:</strong></p>
                <ul class="list-disc list-inside mt-2">
                    @if(!auth()->check())
                        <li>User not authenticated</li>
                    @endif
                    @if(!\App\Models\Feature::isEnabled('gallery'))
                        <li>Gallery feature not enabled by admin</li>
                    @endif
                    @if(auth()->check() && !auth()->user()->show_gallery_images)
                        <li>User has not enabled gallery images in privacy settings</li>
                    @endif
                </ul>
            </div>
        @endif

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Test Controls</h2>
            <div class="space-x-4">
                <button onclick="testLightboxFunctionality()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                    Test Lightbox
                </button>
                <button onclick="checkElements()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                    Check Elements
                </button>
                <button onclick="console.log('Console test')" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                    Console Test
                </button>
            </div>
        </div>
    </div>

    <script>
        console.log('=== GALLERY TEST PAGE LOADED ===');
        
        function testOpenLightbox(imageUrl, imageIndex) {
            console.log('Test lightbox called with:', imageUrl, imageIndex);
            const lightbox = document.getElementById('test-lightbox');
            const lightboxImage = document.getElementById('test-lightbox-image');
            
            if (lightbox && lightboxImage) {
                lightboxImage.src = imageUrl;
                lightbox.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                console.log('Test lightbox opened successfully');
            } else {
                console.error('Test lightbox elements not found');
            }
        }
        
        function closeTestLightbox() {
            const lightbox = document.getElementById('test-lightbox');
            if (lightbox) {
                lightbox.classList.add('hidden');
                document.body.style.overflow = 'auto';
                console.log('Test lightbox closed');
            }
        }
        
        function testLightboxFunctionality() {
            console.log('Testing lightbox with placeholder image...');
            testOpenLightbox('https://via.placeholder.com/500x500/cccccc/666666?text=Test+Image', 0);
        }
        
        function checkElements() {
            console.log('=== ELEMENT CHECK ===');
            console.log('Gallery grid:', document.getElementById('test-gallery-grid'));
            console.log('Gallery items:', document.querySelectorAll('.gallery-item').length);
            console.log('Test lightbox:', document.getElementById('test-lightbox'));
            console.log('Test lightbox image:', document.getElementById('test-lightbox-image'));
        }
        
        // Close lightbox on background click
        document.getElementById('test-lightbox')?.addEventListener('click', function(e) {
            if (e.target === this) {
                closeTestLightbox();
            }
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            const lightbox = document.getElementById('test-lightbox');
            if (!lightbox || lightbox.classList.contains('hidden')) {
                return;
            }
            
            if (e.key === 'Escape') {
                e.preventDefault();
                closeTestLightbox();
            }
        });
    </script>
</body>
</html>
